import "./globals.css";
import <PERSON>ript from "next/script";
import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import { Toaster } from "@/components/ui/toaster";
import { CartProvider } from "@/lib/CartContext";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Analytics } from "@vercel/analytics/next";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "MailPallet - UK Online Shopping & Ghana Nigeria Delivery | Shop Global Products",
  description:
    "Shop thousands of UK products and get them delivered to Ghana & Nigeria. MailPallet offers electronics, fashion, beauty products with free UK address, package consolidation, and door-to-door delivery to Lagos, Accra, and major cities. Save up to 90% on shipping costs. Start shopping now!",
  openGraph: {
    title: "MailPallet - Shop UK Products, Deliver to Africa",
    description:
      "Discover thousands of UK products with fast Africa delivery. Electronics, fashion, beauty & more. Free UK address + package consolidation. Shop now!",
    type: "website",
    url: "https://mailpallet.com",
    siteName: "MailPallet",
  },
  twitter: {
    title: "MailPallet - UK Shopping Made Easy for Ghana & Nigeria",
    description:
      "Shop UK electronics, fashion, beauty products with delivery to Ghana & Nigeria. Free UK address, package consolidation, up to 90% shipping savings!",
    creator: "@mailpallet",
    site: "https://mailpallet.com",
    card: "summary_large_image",
  },
  keywords:
    "uk online shopping, ghana delivery, nigeria delivery, lagos delivery, accra delivery, electronics, fashion, beauty products, package forwarding, international shipping, uk address, ecommerce",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link rel="manifest" href="/manifest.json" />

        <link
          rel="icon"
          type="image/png"
          sizes="196x196"
          href="/favicon-196.png"
        />

        <link rel="apple-touch-icon" href="/apple-icon-180.png" />

        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-HHS9KK75XH"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', 'G-HHS9KK75XH');
          `}
        </Script>
        <Script id="console-suppression" strategy="afterInteractive">
          {`
            if (!window.location.href.includes('localhost')) {
              console.log = () => {};
              console.error = () => {};
              console.warn = () => {};
            }
          `}
        </Script>
        <Script id="structured-data" type="application/ld+json">
          {`
            {
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "MailPallet",
              "url": "https://mailpallet.com",
              "logo": "https://mailpallet.com/assets/imgs/logo.svg",
              "description": "UK online shopping and Ghana Nigeria delivery service offering electronics, fashion, beauty products with package consolidation and international shipping to Lagos, Accra, and major cities.",
              "foundingDate": "2023",
              "contactPoint": {
                "@type": "ContactPoint",
                "contactType": "customer service",
                "email": "<EMAIL>",
                "availableLanguage": "English"
              },
              "areaServed": [
                {
                  "@type": "Country",
                  "name": "Nigeria"
                },
                {
                  "@type": "Country", 
                  "name": "Ghana"
                }
              ],
              "serviceType": [
                "Package Forwarding",
                "International Shipping", 
                "Ecommerce Platform",
                "Logistics Services"
              ]
            }
          `}
        </Script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <CartProvider>
          {children}
          <SpeedInsights />
          <Analytics />
          <Toaster />
        </CartProvider>
      </body>
    </html>
  );
}
