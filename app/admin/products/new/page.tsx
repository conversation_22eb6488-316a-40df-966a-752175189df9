"use client";

import React, { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import ReactMarkdown from "react-markdown";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Command as Cmd, // avoid name clash in some setups
} from "@/components/ui/command";
import {
  Package2,
  DollarSign,
  Truck,
  CheckIcon,
  ChevronsUpDownIcon,
} from "lucide-react";
import { ProductService } from "@/services/product.service";
import {
  Category,
  CategoryService,
  CategoryVariantAttribute,
} from "@/services/category.service";
import { ProductCreationData } from "@/data/models/product.model";
import { cn } from "@/lib/utils";

const generateUUID = (): string => {
  if (
    typeof crypto !== "undefined" &&
    typeof (crypto as any).randomUUID === "function"
  ) {
    return (crypto as any).randomUUID();
  }
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

/**
 * ControlledNumberInput
 *
 * - Renders as text input (avoids browser wheel changing numeric inputs)
 * - Preserves intermediate decimal typing like "1." or ".5"
 * - Emits numeric values (number | undefined) via onChange
 */
function ControlledNumberInput({
  value,
  onChange,
  decimal = false,
  integer = false,
  inputMode,
  placeholder,
  className,
  min,
}: {
  value?: number | undefined;
  onChange: (n: number | undefined) => void;
  decimal?: boolean;
  integer?: boolean;
  inputMode?: "decimal" | "numeric";
  placeholder?: string;
  className?: string;
  min?: number;
}) {
  const [text, setText] = useState<string>(() =>
    value === undefined || value === null ? "" : String(value),
  );
  const focused = useRef(false);

  useEffect(() => {
    const v = value === undefined || value === null ? "" : String(value);
    if (!focused.current) {
      setText(v);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const sanitizeForDecimal = (raw: string) => {
    // allow digits and single dot; preserve leading/trailing dot for typing convenience
    let s = raw.replace(/[^0-9.]/g, "");
    const first = s.indexOf(".");
    if (first !== -1) {
      s = s.slice(0, first + 1) + s.slice(first + 1).replace(/\./g, "");
    }
    return s;
  };

  const sanitizeForInteger = (raw: string) => raw.replace(/[^0-9]/g, "");

  const parseToNumber = (t: string): number | undefined => {
    if (!t) return undefined;
    if (t === ".") return undefined;
    if (integer) {
      const digits = t.replace(/[^0-9]/g, "");
      if (digits === "") return undefined;
      const parsed = Number.parseInt(digits, 10);
      return Number.isFinite(parsed) ? parsed : undefined;
    }
    const parsed = Number(t);
    return Number.isFinite(parsed) ? parsed : undefined;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value;
    const sanitized = integer
      ? sanitizeForInteger(raw)
      : decimal
        ? sanitizeForDecimal(raw)
        : sanitizeForInteger(raw);
    setText(sanitized);
    const parsed = parseToNumber(sanitized);
    onChange(parsed);
  };

  const handleBlur = () => {
    focused.current = false;
    const parsed = parseToNumber(text);
    if (parsed === undefined) {
      setText("");
      onChange(undefined);
    } else {
      setText(String(parsed));
      onChange(parsed);
    }
  };

  const handleFocus = () => {
    focused.current = true;
  };

  return (
    <Input
      type="text"
      inputMode={
        inputMode || (integer ? "numeric" : decimal ? "decimal" : "numeric")
      }
      placeholder={placeholder}
      className={className}
      value={text}
      onChange={handleChange}
      onBlur={handleBlur}
      onFocus={handleFocus}
      aria-valuemin={min}
    />
  );
}

/* -----------------------------
   Zod schemas
   ----------------------------- */

const shippingRateSchema = z.object({
  to_country: z.enum(["UK", "Ghana", "Nigeria", "Rest Of Africa"]),
  to_zone: z.union([
    z.enum(["UK", "Accra", "Outside Accra", "Lagos", "Outside Lagos"]),
    z.literal(""),
  ]),
  base_rate: z.number().min(0).optional(),
  duty: z.number().min(0).optional(),
  rate_per_kg: z.number().min(0).optional(),
  estimated_delivery_days: z.number().min(0).optional(),
  currency: z.string(),
});

const shippingAndDutySchema = z.object({
  to_country: z.enum(["Ghana", "Nigeria"]),
  duty: z.number().optional(),
  currency: z.string().optional(),
});

const externalDetailsSchema = z.object({
  origin_name: z.string().min(1),
  origin_url: z.string().url(),
  is_affiliate: z.boolean(),
  discount_code: z.string().optional(),
});

const internalDetailsSchema = z.object({
  location: z.enum(["UK", "Ghana"]),
  weight_kg: z.number().optional(),
  dimensions_cm: z.object({
    length: z.number().optional(),
    width: z.number().optional(),
    height: z.number().optional(),
  }),
});

const variantItemSchema = z
  .object({
    variant_id: z.string().optional(),
    option_values: z.record(z.string()),
    sale_price: z.number().optional(),
    price: z.number().optional(),
    stock: z.number().optional(),
    discount_percent: z.number().optional(),
  })
  .passthrough();

const masterOptionSchema = z.record(z.array(z.string()));

const productVariantsSchema = z
  .object({
    variants: z.array(variantItemSchema),
    master_options: z.array(masterOptionSchema),
  })
  .optional();

const formSchema = z.object({
  title: z.string().min(1),
  // SKU must be exactly 7 digits
  sku: z.string().regex(/^[0-9]{7}$/, {
    message: "SKU must be exactly 7 digits",
  }),
  description: z.string().min(1),
  currency: z.string().min(1),
  type: z.enum(["external", "internal"]),
  category: z.string().min(1),
  subcategory: z.string().min(1),
  brand: z.string().optional(),
  customBrand: z.string().optional(),
  tags: z.array(z.string()).optional(),
  condition: z.enum(["New", "Used"]),
  admin_notes: z.string().optional(),
  origin_location: z.enum(["UK", "Ghana"]),
  shipping_and_duty: z.array(shippingAndDutySchema),
  details: z.union([externalDetailsSchema, internalDetailsSchema]),
  refundable: z.boolean(),
  refund_policy: z.string().min(1),
  variants: productVariantsSchema.refine(
    (variants) =>
      !!variants &&
      Array.isArray(variants.variants) &&
      variants.variants.length > 0,
    { message: "At least one variant is required" },
  ),
});

/* -----------------------------
   Services and component
   ----------------------------- */

const productService = new ProductService();
const categoryService = new CategoryService();

export default function NewProductPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [primaryImage, setPrimaryImage] = useState<File | null>(null);
  const [primaryImagePreview, setPrimaryImagePreview] = useState<string>("");
  const primaryImageInputRef = useRef<HTMLInputElement | null>(null);
  const [sharedImages, setSharedImages] = useState<File[]>([]);
  const [sharedImagePreviews, setSharedImagePreviews] = useState<string[]>([]);
  const sharedImagesInputRef = useRef<HTMLInputElement | null>(null);

  const [variantAttributes, setVariantAttributes] = useState<
    CategoryVariantAttribute[]
  >([]);
  const [variantImages, setVariantImages] = useState<{
    [variantIndex: number]: File[];
  }>({});
  const [variantImagePreviews, setVariantImagePreviews] = useState<{
    [variantIndex: number]: string[];
  }>({});

  const [customAttributeValues, setCustomAttributeValues] = useState<
    Array<{ variantIndex: number; attribute: string; value: string }>
  >([]);

  const getCustomAttributeValue = (variantIndex: number, attribute: string) => {
    const v = customAttributeValues.find(
      (x) => x.variantIndex === variantIndex && x.attribute === attribute,
    );
    return v?.value || "";
  };

  const setCustomAttributeValue = (
    variantIndex: number,
    attribute: string,
    value: string,
  ) => {
    setCustomAttributeValues((prev) => {
      const filtered = prev.filter(
        (p) => !(p.variantIndex === variantIndex && p.attribute === attribute),
      );
      if (value.trim())
        return [...filtered, { variantIndex, attribute, value }];
      return filtered;
    });
  };

  const [availableBrands, setAvailableBrands] = useState<string[]>([]);
  const [showCustomBrandInput, setShowCustomBrandInput] = useState(false);
  const [customBrandValue, setCustomBrandValue] = useState("");

  useEffect(() => {
    const fetch = async () => {
      try {
        const data = await categoryService.getCategories();
        setCategories(data);
      } catch (err) {
        toast({
          title: "Error",
          description: "Failed to load categories",
          variant: "destructive",
        });
      }
    };
    fetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getDefaultValues = () => ({
    title: "",
    sku: "",
    description: "",
    currency: "GBP",
    type: "internal" as const,
    category: "",
    subcategory: "",
    brand: "none",
    tags: [],
    condition: "New" as const,
    admin_notes: "",
    origin_location: "UK" as const,
    shipping_and_duty: [
      { to_country: "Ghana" as const, duty: undefined, currency: "GBP" },
      { to_country: "Nigeria" as const, duty: undefined, currency: "GBP" },
    ],
    shipping_rates: [
      {
        to_country: "Ghana" as const,
        to_zone: "Accra" as const,
        base_rate: undefined,
        duty: undefined,
        currency: "GBP",
      },
      {
        to_country: "Ghana" as const,
        to_zone: "Outside Accra" as const,
        base_rate: undefined,
        duty: undefined,
        currency: "GBP",
      },
      {
        to_country: "Nigeria" as const,
        to_zone: "Lagos" as const,
        base_rate: undefined,
        duty: undefined,
        currency: "GBP",
      },
      {
        to_country: "Nigeria" as const,
        to_zone: "Outside Lagos" as const,
        base_rate: undefined,
        duty: undefined,
        currency: "GBP",
      },
      {
        to_country: "Rest Of Africa" as const,
        to_zone: "" as const,
        base_rate: undefined,
        duty: undefined,
        currency: "GBP",
      },
    ],
    details: {
      location: "UK" as const,
      weight_kg: undefined,
      dimensions_cm: { length: undefined, width: undefined, height: undefined },
    },
    refundable: false,
    refund_policy: "",
    variants: undefined,
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues() as any,
    mode: "onTouched",
  });

  useEffect(() => {
    const sub = form.watch((_, { name }) => {
      if (
        name &&
        name.includes("variants.variants.") &&
        (name.includes(".price") || name.includes(".sale_price"))
      ) {
        const match = name.match(/variants\.variants\.(\d+)\./);
        if (!match) return;
        const idx = Number(match[1]);
        const variants = form.getValues("variants");
        const v = variants?.variants?.[idx];
        if (!v) return;
        const price = v.price;
        const sale = v.sale_price;
        if (price && sale && price > sale) {
          const disc = Math.round(((price - sale) / price) * 100);
          form.setValue(
            `variants.variants.${idx}.discount_percent` as any,
            disc,
          );
        } else {
          form.setValue(
            `variants.variants.${idx}.discount_percent` as any,
            undefined,
          );
        }
      }
    });
    return () => sub.unsubscribe();
  }, [form]);

  // SKU uniqueness checking (debounced). Shows inline error if taken.
  const skuValue = form.watch("sku");
  const [skuAvailable, setSkuAvailable] = useState<boolean | null>(null);
  const [skuCheckError, setSkuCheckError] = useState<string | null>(null);
  const skuTimerRef = useRef<number | null>(null);

  useEffect(() => {
    // Clear any pending timer if sku changes
    if (skuTimerRef.current) {
      window.clearTimeout(skuTimerRef.current);
      skuTimerRef.current = null;
    }

    // If no valid 7-digit SKU yet, clear availability and errors
    if (!skuValue || !/^[0-9]{7}$/.test(skuValue)) {
      setSkuAvailable(null);
      setSkuCheckError(null);
      form.clearErrors("sku");
      return;
    }

    // Debounce uniqueness check
    skuTimerRef.current = window.setTimeout(async () => {
      try {
        const res = await fetch(
          `/api/products/check-sku?sku=${encodeURIComponent(skuValue)}`,
        );
        if (!res.ok) {
          // mark error so UI can notify user
          setSkuAvailable(null);
          setSkuCheckError(`Server returned ${res.status}`);
          return;
        }
        const json = await res.json();
        const exists = !!json.exists;
        if (exists) {
          form.setError("sku", {
            type: "manual",
            message: "SKU already taken",
          });
          setSkuAvailable(false);
          setSkuCheckError(null);
        } else {
          form.clearErrors("sku");
          setSkuAvailable(true);
          setSkuCheckError(null);
        }
      } catch (err: any) {
        // network or other error -> surface to user
        setSkuAvailable(null);
        setSkuCheckError(err?.message || "Network error");
      } finally {
        skuTimerRef.current = null;
      }
    }, 500);

    return () => {
      if (skuTimerRef.current) {
        window.clearTimeout(skuTimerRef.current);
        skuTimerRef.current = null;
      }
    };
  }, [skuValue, form]);

  const resetFileStates = () => {
    if (primaryImagePreview) URL.revokeObjectURL(primaryImagePreview);
    setPrimaryImage(null);
    setPrimaryImagePreview("");
    sharedImagePreviews.forEach((u) => URL.revokeObjectURL(u));
    setSharedImages([]);
    setSharedImagePreviews([]);
    Object.values(variantImagePreviews)
      .flat()
      .forEach((u) => URL.revokeObjectURL(u));
    setVariantImages({});
    setVariantImagePreviews({});
    setCustomAttributeValues([]);
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach((i: any) => (i.value = ""));
  };

  const handlePrimaryImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (primaryImagePreview) URL.revokeObjectURL(primaryImagePreview);
    setPrimaryImage(file);
    setPrimaryImagePreview(URL.createObjectURL(file));
  };

  const removePrimaryImage = () => {
    if (primaryImagePreview) URL.revokeObjectURL(primaryImagePreview);
    setPrimaryImage(null);
    setPrimaryImagePreview("");
    if (primaryImageInputRef.current) primaryImageInputRef.current.value = "";
  };

  const handleSharedImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setSharedImages((p) => [...p, ...files]);
    const urls = files.map((f) => URL.createObjectURL(f));
    setSharedImagePreviews((p) => [...p, ...urls]);
  };

  const removeSharedImage = (idx: number) => {
    if (sharedImagePreviews[idx]) URL.revokeObjectURL(sharedImagePreviews[idx]);
    setSharedImages((p) => p.filter((_, i) => i !== idx));
    setSharedImagePreviews((p) => p.filter((_, i) => i !== idx));
    if (sharedImagesInputRef.current && sharedImages.length === 1)
      sharedImagesInputRef.current.value = "";
  };

  const handleVariantImagesChange = (
    variantIndex: number,
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(e.target.files || []);
    setVariantImages((prev) => ({
      ...prev,
      [variantIndex]: [...(prev[variantIndex] || []), ...files],
    }));
    const urls = files.map((f) => URL.createObjectURL(f));
    setVariantImagePreviews((prev) => ({
      ...prev,
      [variantIndex]: [...(prev[variantIndex] || []), ...urls],
    }));
  };

  const removeVariantImage = (variantIndex: number, imageIndex: number) => {
    setVariantImages((prev) => ({
      ...prev,
      [variantIndex]: (prev[variantIndex] || []).filter(
        (_, i) => i !== imageIndex,
      ),
    }));
    setVariantImagePreviews((prev) => {
      const arr = prev[variantIndex] || [];
      if (arr[imageIndex]) URL.revokeObjectURL(arr[imageIndex]);
      return {
        ...prev,
        [variantIndex]: arr.filter((_, i) => i !== imageIndex),
      };
    });
  };

  const addVariant = () => {
    const current = form.getValues("variants");
    const option_values: Record<string, any> = {};
    variantAttributes.forEach((attr) => {
      const hasCustom = (attr.values || []).includes("Custom");
      const defaultValue = hasCustom
        ? (attr.values || []).find((v) => v !== "Custom") || ""
        : attr.values && attr.values.length > 0
          ? attr.values[0]
          : "";
      option_values[attr.name] = String(defaultValue);
    });

    const newVariant = {
      option_values,
      sale_price: undefined,
      price: undefined,
      stock: undefined,
      currency: "GBP",
    };
    if (current) {
      form.setValue("variants", {
        ...current,
        variants: [...(current.variants || []), newVariant],
      });
    } else {
      form.setValue("variants", {
        variants: [newVariant],
        master_options: variantAttributes.map((a) => ({
          [a.name]: (a.values || []).filter((v) => v !== "Custom").map(String),
        })),
      });
    }
  };

  const removeVariant = (index: number) => {
    const current = form.getValues("variants");
    if (current && current.variants) {
      form.setValue("variants", {
        ...current,
        variants: current.variants.filter((_: any, i: number) => i !== index),
      });
    }

    const previews = variantImagePreviews[index] || [];
    previews.forEach((u) => URL.revokeObjectURL(u));
    setVariantImages((prev) => {
      const n = { ...prev };
      delete n[index];
      return n;
    });
    setVariantImagePreviews((prev) => {
      const n = { ...prev };
      delete n[index];
      return n;
    });
    setCustomAttributeValues((prev) =>
      prev.filter((p) => p.variantIndex !== index),
    );
  };

  const productType = form.watch("type");
  const refundable = form.watch("refundable");
  const selectedSubcategory = form.watch("subcategory");

  useEffect(() => {
    if (productType === "external") {
      const current = form.getValues("details");
      if (!current || !("origin_name" in current)) {
        form.setValue("details", {
          origin_name: "",
          origin_url: "",
          is_affiliate: false,
          discount_code: "",
        });
      }
    } else {
      const current = form.getValues("details");
      if (!current || !("location" in current)) {
        form.setValue("details", {
          location: "UK",
          weight_kg: undefined,
          dimensions_cm: {
            length: undefined,
            width: undefined,
            height: undefined,
          },
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productType]);

  useEffect(() => {
    form.setValue(
      "refund_policy",
      refundable
        ? "Item is refundable. Contact our support."
        : "Item is non refundable. Contact our support for any issues.",
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refundable]);

  useEffect(() => {
    const fetch = async () => {
      if (!selectedSubcategory) {
        setVariantAttributes([]);
        setAvailableBrands([]);
        setCustomAttributeValues([]);
        form.setValue("variants", undefined);
        form.setValue("brand", "none");
        setVariantImages({});
        setVariantImagePreviews({});
        return;
      }

      try {
        setVariantAttributes([]);
        setCustomAttributeValues([]);
        const attrs =
          await categoryService.getCategoryVariantAttributes(
            selectedSubcategory,
          );
        setVariantAttributes(attrs || []);
        const brands =
          await categoryService.getCategoryBrands(selectedSubcategory);
        setAvailableBrands(brands || []);
        form.setValue("variants", undefined);
        form.setValue("brand", "none");
        setVariantImages({});
        setVariantImagePreviews({});

        if (attrs && attrs.length > 0) {
          const option_values: Record<string, any> = {};
          attrs.forEach((attr) => {
            const hasCustom = (attr.values || []).includes("Custom");
            const defaultValue = hasCustom
              ? (attr.values || []).find((v) => v !== "Custom") || ""
              : attr.values && attr.values.length > 0
                ? attr.values[0]
                : "";
            option_values[attr.name] = String(defaultValue);
          });

          const defaultVariant = {
            option_values,
            sale_price: undefined,
            price: undefined,
            stock: undefined,
            currency: "GBP",
          };
          form.setValue("variants", {
            variants: [defaultVariant],
            master_options: attrs.map((a) => ({
              [a.name]: (a.values || [])
                .filter((v) => v !== "Custom")
                .map(String),
            })),
          });
        }
      } catch (err) {
        console.error("Error fetching category data:", err);
        setVariantAttributes([]);
        setAvailableBrands([]);
        setCustomAttributeValues([]);
      }
    };

    fetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedSubcategory]);

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (!primaryImage) {
      toast({
        title: "Error",
        description: "Please select a primary product image",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      let preparedVariants = data.variants;

      if (preparedVariants && preparedVariants.variants) {
        const variantsWithIds = preparedVariants.variants.map(
          (variant: any, variantIndex: number) => {
            const transformed: Record<string, any> = {};
            Object.entries(variant.option_values || {}).forEach(([k, v]) => {
              if (typeof v === "string") {
                if (v === "Custom") {
                  const customVal = getCustomAttributeValue(variantIndex, k);
                  if (customVal.trim())
                    transformed[k] = { value: customVal, is_custom: true };
                  else transformed[k] = v;
                } else {
                  transformed[k] = v;
                }
              } else {
                transformed[k] = v;
              }
            });

            return {
              ...variant,
              option_values: transformed,
              variant_id: variant.variant_id || generateUUID(),
              discount_percent:
                variant.sale_price && variant.price
                  ? Math.round(
                      ((variant.price - variant.sale_price) / variant.price) *
                        100,
                    )
                  : undefined,
            };
          },
        );

        const master_options: Array<Record<string, string[]>> = [];
        if (variantAttributes.length > 0) {
          variantAttributes.forEach((attr) => {
            const set = new Set<string>();
            variantsWithIds.forEach((v) => {
              const ov = v.option_values?.[attr.name];
              if (!ov) return;
              if (typeof ov === "object" && ov?.value) {
                set.add(String(ov.value));
              } else if (typeof ov === "string" && ov !== "Custom") {
                set.add(ov);
              }
            });
            const arr = Array.from(set);
            if (arr.length > 0) master_options.push({ [attr.name]: arr });
          });
        }

        preparedVariants = { variants: variantsWithIds, master_options };
      }

      const { currency, variants, shipping_and_duty, ...productData } =
        data as any;

      const shippingAndDuty = data.shipping_and_duty || [];
      const rebuiltShippingRates: any[] = [];

      shippingAndDuty.forEach((entry: any, idx: number) => {
        if (!entry || !entry.to_country) return;
        const duty = entry.duty;
        if (duty === undefined || duty === null) {
          throw new Error(
            `Duty is required for ${entry.to_country} (entry ${idx})`,
          );
        }
        const cur = entry.currency || "GBP";
        if (entry.to_country === "Ghana") {
          rebuiltShippingRates.push({
            to_country: "Ghana",
            to_zone: "Accra",
            base_rate: 0,
            duty,
            currency: cur,
          });
          rebuiltShippingRates.push({
            to_country: "Ghana",
            to_zone: "Outside Accra",
            base_rate: 0,
            duty,
            currency: cur,
          });
        } else if (entry.to_country === "Nigeria") {
          rebuiltShippingRates.push({
            to_country: "Nigeria",
            to_zone: "Lagos",
            base_rate: 0,
            duty,
            currency: cur,
          });
          rebuiltShippingRates.push({
            to_country: "Nigeria",
            to_zone: "Outside Lagos",
            base_rate: 0,
            duty,
            currency: cur,
          });
        }
      });

      const transformedProductData = {
        ...productData,
        brand:
          productData.brand === "custom"
            ? customBrandValue
            : productData.brand === "none"
              ? undefined
              : productData.brand,
        shipping_rates: rebuiltShippingRates,
      };

      const firstVariant = preparedVariants?.variants?.[0];
      if (!firstVariant) throw new Error("At least one variant is required");

      const creationData: ProductCreationData = {
        product: {
          ...transformedProductData,
          tags: data.tags || [],
          primary_data: {
            sale_price: firstVariant.sale_price || 0,
            price: firstVariant.price || 0,
            discount_percent: firstVariant.discount_percent || 0,
          },
          currency: "GBP",
          variants: preparedVariants,
          primary_image: { url: "", name: "" },
          shared_images: [],
        },
        primaryImage,
        shared_images: sharedImages,
        variant_images: variantImages,
      };

      await productService.createProduct(creationData);

      toast({ title: "Success", description: "Product created successfully" });
      form.reset(getDefaultValues());
      resetFileStates();
    } catch (err) {
      console.error(err);
      toast({
        title: "Error",
        description: "Failed to create product",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="max-w-7xl mx-auto p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold tracking-tight">New Product</h1>
            <p className="text-muted-foreground mt-2">
              Create a new product listing
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-6 items-start">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package2 className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Product title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sku"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SKU</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="7-digit SKU (e.g., 0000123)"
                          value={field.value ?? ""}
                          onChange={(e) => {
                            // allow only digits, max 7 characters
                            const digits = e.target.value
                              .replace(/\D/g, "")
                              .slice(0, 7);
                            field.onChange(digits);
                            // while typing, clear any previous manual errors
                            if (form.formState.errors?.sku) {
                              form.clearErrors("sku");
                            }
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Exactly 7 digits. The form checks uniqueness as you
                        type.
                        {skuCheckError
                          ? ` Error checking SKU: ${skuCheckError}`
                          : skuAvailable === null &&
                              field.value &&
                              field.value.length === 7
                            ? " Checking..."
                            : skuAvailable === true
                              ? " SKU available"
                              : skuAvailable === false
                                ? " SKU already taken"
                                : ""}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Markdown Supported)</FormLabel>
                      <FormControl>
                        <Tabs defaultValue="write" className="w-full">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="write">Write</TabsTrigger>
                            <TabsTrigger value="preview">Preview</TabsTrigger>
                          </TabsList>
                          <TabsContent value="write">
                            <Textarea
                              placeholder="Product description"
                              className="min-h-[150px]"
                              {...field}
                            />
                          </TabsContent>
                          <TabsContent value="preview">
                            <div className="min-h-[150px] p-3 border rounded-md bg-muted/50">
                              {field.value ? (
                                <div className="prose prose-sm max-w-none dark:prose-invert">
                                  <ReactMarkdown>{field.value}</ReactMarkdown>
                                </div>
                              ) : (
                                <div className="text-muted-foreground text-sm">
                                  Nothing to preview
                                </div>
                              )}
                            </div>
                          </TabsContent>
                        </Tabs>
                      </FormControl>
                      <FormDescription>
                        You can use markdown formatting. Switch to Preview tab
                        to see how it will look.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="internal">Internal</SelectItem>
                            <SelectItem value="external">External</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="condition"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Condition</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select condition" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="New">New</SelectItem>
                            <SelectItem value="Used">Used</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => {
                      const [open, setOpen] = useState(false);
                      const parentCategories = categories.filter(
                        (cat) => cat.parent_id === null,
                      );
                      return (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={open}
                                  className="w-full justify-between"
                                >
                                  {field.value
                                    ? parentCategories.find(
                                        (c) => c.id === field.value,
                                      )?.name
                                    : "Select category..."}
                                  <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput placeholder="Search category..." />
                                <CommandList>
                                  <CommandEmpty>
                                    No category found.
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {parentCategories.map((category) => (
                                      <CommandItem
                                        key={category.id}
                                        value={category.name}
                                        onSelect={() => {
                                          field.onChange(category.id);
                                          form.setValue("subcategory", "");
                                          setOpen(false);
                                        }}
                                      >
                                        <CheckIcon
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            field.value === category.id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {category.name}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="subcategory"
                    render={({ field }) => {
                      const [open, setOpen] = useState(false);
                      const selectedCategoryId = form.watch("category");
                      const subcategories = categories.filter(
                        (cat) => cat.parent_id === selectedCategoryId,
                      );
                      return (
                        <FormItem>
                          <FormLabel>Subcategory</FormLabel>
                          <Popover open={open} onOpenChange={setOpen}>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  aria-expanded={open}
                                  className="w-full justify-between"
                                  disabled={!selectedCategoryId}
                                >
                                  {field.value
                                    ? subcategories.find(
                                        (s) => s.id === field.value,
                                      )?.name
                                    : selectedCategoryId
                                      ? "Select subcategory..."
                                      : "Select category first"}
                                  <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-full p-0">
                              <Command>
                                <CommandInput placeholder="Search subcategory..." />
                                <CommandList>
                                  <CommandEmpty>
                                    No subcategory found.
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {subcategories.map((subcategory) => (
                                      <CommandItem
                                        key={subcategory.id}
                                        value={subcategory.name}
                                        onSelect={() => {
                                          field.onChange(subcategory.id);
                                          setOpen(false);
                                        }}
                                      >
                                        <CheckIcon
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            field.value === subcategory.id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {subcategory.name}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Brand</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          setShowCustomBrandInput(
                            value.toLowerCase() === "custom",
                          );
                        }}
                        defaultValue={field.value}
                        disabled={availableBrands.length === 0}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                availableBrands.length === 0
                                  ? "Select subcategory first"
                                  : "Select brand..."
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No brand</SelectItem>
                          {availableBrands.map((b) => (
                            <SelectItem key={b} value={b}>
                              {b}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {showCustomBrandInput && (
                  <FormField
                    control={form.control}
                    name="customBrand"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Custom Brand Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter custom brand name"
                            {...field}
                            value={customBrandValue}
                            onChange={(e) =>
                              setCustomBrandValue(e.target.value)
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Enter the name of the custom brand.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="origin_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Origin Location</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select location" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="UK">UK</SelectItem>
                          <SelectItem value="Ghana">Ghana</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tags"
                  render={({ field }) => {
                    const [inputValue, setInputValue] = useState(
                      field.value?.join(", ") || "",
                    );
                    useEffect(
                      () => setInputValue(field.value?.join(", ") || ""),
                      [field.value],
                    );
                    const process = (v: string) => {
                      const tags = v
                        .split(",")
                        .map((t) => t.trim())
                        .filter((t) => t.length > 0);
                      field.onChange(tags.length > 0 ? tags : []);
                    };
                    return (
                      <FormItem>
                        <FormLabel>Tags</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter tags separated by commas"
                            className="min-h-[80px]"
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            onBlur={() => process(inputValue)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="admin_notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Admin Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Internal notes..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Internal notes visible only to administrators
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Return Policy
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="refundable"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Is this product refundable?</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={(val) =>
                              field.onChange(val === "true")
                            }
                            value={field.value ? "true" : "false"}
                            className="flex flex-col space-y-1"
                          >
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="true" />
                              </FormControl>
                              <FormLabel className="font-normal">Yes</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="false" />
                              </FormControl>
                              <FormLabel className="font-normal">No</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="refund_policy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Refund Policy</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Refund policy details"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          This field is automatically populated based on your
                          selection.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Product Images</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <FormLabel className="text-sm font-medium">
                        Thumbnail Image *
                      </FormLabel>
                      <Input
                        ref={primaryImageInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handlePrimaryImageChange}
                        className="mb-2 mt-1"
                      />
                      <FormDescription>
                        This is the image that is displayed on the shop page /
                        results page (required)
                      </FormDescription>
                      {primaryImagePreview && (
                        <div className="mt-4 relative inline-block">
                          <img
                            src={primaryImagePreview}
                            alt="Primary preview"
                            className="w-32 h-32 object-cover rounded-md border-2 border-blue-500"
                          />
                          <div className="absolute top-1 left-1 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                            Primary
                          </div>
                          <button
                            type="button"
                            onClick={removePrimaryImage}
                            className="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 text-xs"
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>

                    <div>
                      <FormLabel className="text-sm font-medium">
                        Shared Images (Optional)
                      </FormLabel>
                      <Input
                        ref={sharedImagesInputRef}
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={handleSharedImagesChange}
                        className="mb-2 mt-1"
                      />
                      <FormDescription>
                        Upload additional product images that are displayed
                        across all variants.
                      </FormDescription>
                      {sharedImagePreviews.length > 0 && (
                        <div className="grid grid-cols-3 gap-4 mt-4">
                          {sharedImagePreviews.map((url, i) => (
                            <div key={i} className="relative">
                              <img
                                src={url}
                                alt={`Shared ${i + 1}`}
                                className="w-full h-32 object-cover rounded-md"
                              />
                              <button
                                type="button"
                                onClick={() => removeSharedImage(i)}
                                className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Product Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {productType === "external" ? (
                    <>
                      <FormField
                        control={form.control}
                        name="details.origin_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Origin Store Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Store name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="details.origin_url"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Product URL</FormLabel>
                            <FormControl>
                              <Input placeholder="https://..." {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="details.is_affiliate"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Is Affiliate Product</FormLabel>
                              <FormDescription>
                                Check this if the product is associated with an
                                affiliate program
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="details.discount_code"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Discount Code (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="Discount code" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  ) : (
                    <>
                      <FormField
                        control={form.control}
                        name="details.weight_kg"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight (kg) *</FormLabel>
                            <FormControl>
                              <ControlledNumberInput
                                decimal
                                value={field.value as any}
                                onChange={(n) => field.onChange(n)}
                                inputMode="decimal"
                                placeholder="0.00"
                                className="min-h-[38px]"
                                min={0}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          Dimensions (cm) *
                        </label>
                        <div className="grid grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.length"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <ControlledNumberInput
                                    decimal
                                    value={field.value as any}
                                    onChange={(n) => field.onChange(n)}
                                    inputMode="decimal"
                                    placeholder="Length"
                                    className="min-h-[38px]"
                                    min={0}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.width"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <ControlledNumberInput
                                    decimal
                                    value={field.value as any}
                                    onChange={(n) => field.onChange(n)}
                                    inputMode="decimal"
                                    placeholder="Width"
                                    className="min-h-[38px]"
                                    min={0}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="details.dimensions_cm.height"
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <ControlledNumberInput
                                    decimal
                                    value={field.value as any}
                                    onChange={(n) => field.onChange(n)}
                                    inputMode="decimal"
                                    placeholder="Height"
                                    className="min-h-[38px]"
                                    min={0}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Shipping & Duty
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  {["Ghana", "Nigeria"].map((country, index) => (
                    <div key={country} className="p-4 border rounded-lg">
                      <div className="mb-2">
                        <label className="text-sm font-medium">
                          To Country
                        </label>
                        <div className="mt-1 px-3 py-2 border rounded-md bg-muted text-sm">
                          {country}
                        </div>
                      </div>

                      <FormField
                        control={form.control}
                        name={`shipping_and_duty.${index}.duty`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Duty</FormLabel>
                            <FormControl>
                              <ControlledNumberInput
                                integer
                                value={field.value as any}
                                onChange={(n) => field.onChange(n)}
                                inputMode="numeric"
                                placeholder="Duty"
                                className="min-h-[38px]"
                                min={0}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {variantAttributes.length > 0 && (
            <div className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package2 className="h-5 w-5" />
                    Product Info
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    At least one variant is required. The first variant's
                    pricing will be used as the primary product data.
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4">
                    {form
                      .watch("variants")
                      ?.variants?.map((_: any, variantIndex: number) => (
                        <div
                          key={variantIndex}
                          className="space-y-4 p-4 border rounded-lg"
                        >
                          <div className="flex justify-between items-center">
                            <h4 className="font-medium">
                              {variantIndex === 0
                                ? "Main Product Info"
                                : `Variant ${variantIndex}`}
                            </h4>
                            {variantIndex !== 0 && (
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                onClick={() => removeVariant(variantIndex)}
                              >
                                Remove
                              </Button>
                            )}
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            {variantAttributes.map((attr) => {
                              const hasCustom = (attr.values || []).includes(
                                "Custom",
                              );
                              return (
                                <FormField
                                  key={attr.name}
                                  control={form.control}
                                  name={
                                    `variants.variants.${variantIndex}.option_values.${attr.name}` as any
                                  }
                                  render={({ field }) => {
                                    const isCustomSelected =
                                      hasCustom && field.value === "Custom";
                                    return (
                                      <FormItem>
                                        <FormLabel>{attr.name}</FormLabel>
                                        <FormControl>
                                          <div className="space-y-2">
                                            {attr.options || attr.values ? (
                                              <Select
                                                onValueChange={(v) =>
                                                  field.onChange(v)
                                                }
                                                value={
                                                  isCustomSelected
                                                    ? "Custom"
                                                    : typeof field.value ===
                                                        "string"
                                                      ? field.value
                                                      : ""
                                                }
                                              >
                                                <FormControl>
                                                  <SelectTrigger>
                                                    <SelectValue
                                                      placeholder={`Select ${attr.name}`}
                                                    />
                                                  </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                  {(
                                                    attr.options ||
                                                    attr.values ||
                                                    []
                                                  ).map((opt: any) => (
                                                    <SelectItem
                                                      key={opt}
                                                      value={String(opt)}
                                                    >
                                                      {String(opt)}
                                                    </SelectItem>
                                                  ))}
                                                </SelectContent>
                                              </Select>
                                            ) : (
                                              <Input
                                                type="text"
                                                placeholder={`Enter ${attr.name}`}
                                                value={
                                                  typeof field.value ===
                                                  "string"
                                                    ? field.value
                                                    : ""
                                                }
                                                onChange={(e) =>
                                                  field.onChange(e.target.value)
                                                }
                                              />
                                            )}

                                            {isCustomSelected && (
                                              <div>
                                                <p className="text-sm text-gray-600 mb-1">
                                                  Custom {attr.name}:
                                                </p>
                                                <Input
                                                  type="text"
                                                  placeholder={`Enter custom ${attr.name}`}
                                                  value={getCustomAttributeValue(
                                                    variantIndex,
                                                    attr.name,
                                                  )}
                                                  onChange={(e) =>
                                                    setCustomAttributeValue(
                                                      variantIndex,
                                                      attr.name,
                                                      e.target.value,
                                                    )
                                                  }
                                                />
                                              </div>
                                            )}
                                          </div>
                                        </FormControl>
                                        <FormMessage />
                                      </FormItem>
                                    );
                                  }}
                                />
                              );
                            })}
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.price` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Base Price</FormLabel>
                                  <FormControl>
                                    <ControlledNumberInput
                                      decimal
                                      value={field.value as any}
                                      onChange={(n) => field.onChange(n)}
                                      inputMode="decimal"
                                      placeholder="0.00"
                                      className="min-h-[38px]"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.sale_price` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Sale Price</FormLabel>
                                  <FormControl>
                                    <ControlledNumberInput
                                      decimal
                                      value={field.value as any}
                                      onChange={(n) => field.onChange(n)}
                                      inputMode="decimal"
                                      placeholder="0.00"
                                      className="min-h-[38px]"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name={
                                `variants.variants.${variantIndex}.discount_percent` as any
                              }
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Discount Percentage</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="text"
                                      placeholder="Auto-calculated"
                                      readOnly
                                      {...field}
                                      value={field.value ?? ""}
                                      className="bg-gray-50 cursor-not-allowed"
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Automatically calculated based on base price
                                    and sale price (shown as percentage)
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={form.control}
                            name={
                              `variants.variants.${variantIndex}.stock` as any
                            }
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Stock Quantity</FormLabel>
                                <FormControl>
                                  <ControlledNumberInput
                                    integer
                                    value={field.value as any}
                                    onChange={(n) => field.onChange(n)}
                                    inputMode="numeric"
                                    placeholder="0"
                                    className="min-h-[38px]"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div>
                            <FormLabel className="text-sm font-medium">
                              Variant Images
                            </FormLabel>
                            <Input
                              type="file"
                              accept="image/*"
                              multiple
                              onChange={(e) =>
                                handleVariantImagesChange(variantIndex, e)
                              }
                              className="mb-2 mt-1"
                            />
                            <FormDescription>
                              Upload images specific to this variant
                            </FormDescription>

                            {variantImagePreviews[variantIndex] &&
                              variantImagePreviews[variantIndex].length > 0 && (
                                <div className="grid grid-cols-3 gap-4 mt-4">
                                  {variantImagePreviews[variantIndex].map(
                                    (url, i) => (
                                      <div key={i} className="relative">
                                        <img
                                          src={url}
                                          alt={`Variant ${variantIndex} image ${i + 1}`}
                                          className="w-full h-32 object-cover rounded-md"
                                        />
                                        <button
                                          type="button"
                                          onClick={() =>
                                            removeVariantImage(variantIndex, i)
                                          }
                                          className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                                        >
                                          ×
                                        </button>
                                      </div>
                                    ),
                                  )}
                                </div>
                              )}
                          </div>
                        </div>
                      ))}
                  </div>

                  <Button type="button" variant="outline" onClick={addVariant}>
                    Add Variant
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="mt-6 flex justify-end gap-4">
            <Button
              variant="outline"
              type="button"
              onClick={() => {
                form.reset(getDefaultValues());
                resetFileStates();
              }}
            >
              Reset Form
            </Button>
            <Button type="submit" disabled={isLoading || skuAvailable !== true}>
              {isLoading ? "Creating..." : "Create Product"}
            </Button>
          </div>

          {(skuCheckError ||
            skuAvailable === false ||
            (skuAvailable === null && skuValue && skuValue.length === 7)) && (
            <div className="mt-2 text-sm text-red-500">
              {skuCheckError
                ? `SKU check failed: ${skuCheckError}`
                : skuAvailable === false
                  ? "SKU already taken"
                  : "Checking SKU..."}
            </div>
          )}
        </div>
      </form>
    </Form>
  );
}
