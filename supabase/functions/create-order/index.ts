/*
Updated Supabase Edge Function: create-order
Adds a Discord webhook embed (server-side) after an order is successfully created.
Stores and reads webhook URL from the environment variable: DISCORD_WEBHOOK_URL

Notes:
- The webhook call is performed server-side only and will not be exposed to the client.
- Failures sending the webhook are logged but do not prevent the order creation response.
*/

import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { corsHeaders } from "../_shared/config.ts";
import { createClient } from "jsr:@supabase/supabase-js";
import Stripe from "https://esm.sh/stripe@15.8.0?target=deno";
import { serve } from "https://deno.land/std@0.208.0/http/server.ts";

// Initialize Stripe
const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
  apiVersion: "2023-10-16",
  httpClient: Stripe.createFetchHttpClient(),
});

const supabaseClient = createClient(
  Deno.env.get("DB_URL")!,
  Deno.env.get("SERVICE_ROLE_KEY")!
);

// Generate unique order number
function generateOrderNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `MP-${timestamp}-${random}`.toUpperCase();
}

// Generate unique invoice number
function generateInvoiceNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 3);
  return `INV-${timestamp}-${random}`.toUpperCase();
}

function buildItemsSummary(items: any[], maxItems = 8) {
  if (!Array.isArray(items) || items.length === 0) return "No items";
  const preview = items.slice(0, maxItems).map((it) => {
    const title =
      it.product_snapshot?.title ||
      it.product_snapshot?.name ||
      it.product_id ||
      "Item";
    const qty = it.quantity ?? it.qty ?? 1;
    const price =
      typeof it.unit_price !== "undefined" ? it.unit_price : it.price ?? "";
    const currency = it.currency ? `${it.currency} ` : "";
    return `• ${title} x${qty} — ${currency}${price}`;
  });
  let summary = preview.join("\n");
  if (items.length > maxItems) {
    summary += `\n...and ${items.length - maxItems} more`;
  }
  // ensure we don't exceed Discord embed field length ~1024 chars; truncate if necessary
  if (summary.length > 1000) {
    summary = summary.substring(0, 997) + "...";
  }
  return summary;
}

async function sendDiscordWebhook(
  orderResult: any,
  orderData: any,
  receiptUrl: string | null
) {
  const webhookUrl = Deno.env.get("DISCORD_WEBHOOK_URL");
  if (!webhookUrl) {
    console.warn(
      "DISCORD_WEBHOOK_URL not configured; skipping Discord notification."
    );
    return;
  }

  // Derive a database identifier from DB_URL for clarity (e.g. DB name or host)
  const dbUrl = Deno.env.get("DB_URL") || "";
  let dbIdentifier = dbUrl;
  try {
    const parsed = new URL(dbUrl);
    // prefer pathname (db name) if present, else host
    dbIdentifier =
      (parsed.pathname && parsed.pathname.replace(/^\//, "")) ||
      parsed.host ||
      dbUrl;
  } catch {
    // fallback to raw string
    dbIdentifier = dbUrl;
  }

  const itemsSummary = buildItemsSummary(orderData.items || []);
  const totalAmount =
    typeof orderData.total_amount !== "undefined"
      ? orderData.total_amount
      : orderData.totalAmount ?? 0;
  const currency = orderData.currency || "GBP";
  const shippingCost = orderData.shipping_cost ?? orderData.shippingCost ?? 0;
  const discountAmount =
    orderData.discount_amount ?? orderData.discountAmount ?? 0;

  const embed: any = {
    title: `New Order — ${orderData.order_number || "Unknown"}`,
    color: 0x00b0f4,
    fields: [
      { name: "Order ID", value: `${orderResult?.id ?? "N/A"}`, inline: true },
      { name: "Order #", value: `${orderData.order_number}`, inline: true },
      { name: "User", value: `${orderData.user_id ?? "N/A"}`, inline: true },
      {
        name: "Email",
        value: `${orderData.user_email ?? orderData.email ?? "N/A"}`,
        inline: true,
      },
      {
        name: "Total",
        value: `${currency} ${Number(totalAmount).toFixed(2)}`,
        inline: true,
      },
      {
        name: "Shipping",
        value: `${currency} ${Number(shippingCost).toFixed(2)}`,
        inline: true,
      },
      {
        name: "Discount",
        value: `${currency} ${Number(discountAmount).toFixed(2)}`,
        inline: true,
      },
      {
        name: "Receipt",
        value: receiptUrl ? `[View receipt](${receiptUrl})` : "N/A",
        inline: false,
      },
      { name: "Database", value: `${dbIdentifier}`, inline: true },
      { name: "Created At", value: new Date().toISOString(), inline: true },
      { name: "Items", value: itemsSummary, inline: false },
    ],
    timestamp: new Date().toISOString(),
  };

  const payload = {
    embeds: [embed],
  };

  try {
    const resp = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!resp.ok) {
      const txt = await resp.text();
      console.error("Discord webhook failed:", resp.status, txt);
    } else {
      console.log("Discord webhook sent for order:", orderData.order_number);
    }
  } catch (err) {
    console.error("Failed to send Discord webhook:", err);
  }
}

serve(async (req: Request) => {
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (req.method === "POST") {
      const {
        userId,
        email,
        items,
        subtotal,
        taxAmount,
        shippingCost,
        totalAmount,
        currency,
        paymentIntentId,
        discountAmount,
        originalAmount,
        shipping_address,
      } = await req.json();

      // Validate required fields
      if (
        !userId ||
        !email ||
        !items ||
        !Array.isArray(items) ||
        items.length === 0 ||
        !paymentIntentId
      ) {
        return new Response(
          JSON.stringify({ success: false, error: "Missing required fields" }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Retrieve payment intent to get receipt URL
      const paymentIntent = await stripe.paymentIntents.retrieve(
        paymentIntentId
      );
      const charge = paymentIntent.latest_charge as string;
      let receiptUrl = null;

      if (charge) {
        const chargeDetails = await stripe.charges.retrieve(charge);
        receiptUrl = chargeDetails.receipt_url;
      }

      // Generate order and invoice numbers
      const orderNumber = generateOrderNumber();
      const invoiceNumber = generateInvoiceNumber();

      // Prepare order data
      const orderData = {
        order_number: orderNumber,
        user_id: userId,
        user_email: email,
        items: items,
        subtotal: Number(subtotal) || 0,
        shipping_cost: Number(shippingCost) || 0,
        discount_amount: Number(discountAmount) || 0,
        total_amount: Number(totalAmount),
        currency: currency || "GBP",
        source: "web",
        marketing_opt_in: false,
        shipping_address: shipping_address || null,
        payment_details: {
          method: "card",
          status: "completed",
          processor: "stripe",
          transaction_id: paymentIntentId,
          processor_transaction_id: paymentIntent.id,
          subtotal: Number(subtotal) || 0,
          shipping_cost: Number(shippingCost) || 0,
          discount_amount: Number(discountAmount) || 0,
          total_amount: Number(totalAmount),
          currency: currency || "GBP",
          payment_date: new Date().toISOString(),
          invoice_number: invoiceNumber,
          invoice_url: receiptUrl,
        },
        status_history: [
          {
            status: "confirmed",
            timestamp: new Date().toISOString(),
            notes: "Order confirmed after successful payment",
          },
        ],
        created_at: new Date().toISOString(),
        confirmed_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Insert order into database
      const { data: orderResult, error: orderError } = await supabaseClient
        .from("mp_ecommerce_orders")
        .insert([orderData])
        .select()
        .single();

      if (orderError) {
        console.error("Order creation error:", orderError);
        return new Response(
          JSON.stringify({
            success: false,
            error: "Failed to create order: " + orderError.message,
          }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Send Discord webhook notification (do not block order creation on failure)
      try {
        // fire and await so that we can log results; still don't fail the API response on webhook error
        await sendDiscordWebhook(orderResult, orderData, receiptUrl);
      } catch (err) {
        // sendDiscordWebhook should already catch and log; this is a safeguard
        console.error("Error while sending Discord webhook:", err);
      }

      // Update inventory for each item (if needed)
      for (const item of items) {
        // Check if this item has a variant_id (variant-specific stock)
        if (item.variant_id) {
          // Handle variant-specific stock reduction
          const { data: product, error: fetchError } = await supabaseClient
            .from("mp_ecommerce_products")
            .select("variants")
            .eq("id", item.product_id)
            .single();

          if (fetchError || !product) {
            console.warn(
              `Failed to fetch product variants for product ${item.product_id}:`,
              fetchError
            );
            continue;
          }

          const variants = product.variants as any;
          if (
            variants &&
            variants.variants &&
            Array.isArray(variants.variants)
          ) {
            // Find the specific variant and update its stock
            const updatedVariants = variants.variants.map((variant: any) => {
              if (variant.variant_id === item.variant_id) {
                const currentStock = variant.stock || 0;
                const newStock = Math.max(0, currentStock - item.quantity);
                console.log(
                  `Updating variant ${item.variant_id} stock from ${currentStock} to ${newStock}`
                );
                return {
                  ...variant,
                  stock: newStock,
                };
              }
              return variant;
            });

            // Update the variants in the database
            const { error: variantUpdateError } = await supabaseClient
              .from("mp_ecommerce_products")
              .update({
                variants: {
                  ...variants,
                  variants: updatedVariants,
                },
              })
              .eq("id", item.product_id);

            if (variantUpdateError) {
              console.warn(
                `Failed to update variant stock for product ${item.product_id}, variant ${item.variant_id}:`,
                variantUpdateError
              );
            }
          } else {
            console.warn(
              `Product ${item.product_id} has variant_id but no variants data found`
            );
          }
        } else {
          // Handle legacy products without variants (if any still exist)
          const { data: product, error: fetchError } = await supabaseClient
            .from("mp_ecommerce_products")
            .select("details")
            .eq("id", item.product_id)
            .single();

          if (fetchError || !product) {
            console.warn(
              `Failed to fetch product details for product ${item.product_id}:`,
              fetchError
            );
            continue;
          }

          // Check if this is an internal product with stock in details
          const productDetails = product.details as any;
          if (productDetails && typeof productDetails.stock === "number") {
            // Calculate the new stock quantity
            const newStock = Math.max(0, productDetails.stock - item.quantity);
            console.log(
              `Updating product ${item.product_id} stock from ${productDetails.stock} to ${newStock}`
            );

            // Update the stock in the JSONB details column
            const updatedDetails = {
              ...productDetails,
              stock: newStock,
            };

            const { error: inventoryError } = await supabaseClient
              .from("mp_ecommerce_products")
              .update({
                details: updatedDetails,
              })
              .eq("id", item.product_id);

            if (inventoryError) {
              console.warn(
                `Failed to update inventory for product ${item.product_id}:`,
                inventoryError
              );
            }
          } else {
            console.warn(
              `Product ${item.product_id} is not an internal product or missing stock information`
            );
          }
        }
      }

      return new Response(
        JSON.stringify({
          success: true,
          orderId: orderResult.id,
          orderNumber: orderNumber,
          receiptUrl: receiptUrl,
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({ error: `Method ${req.method} not allowed` }),
      {
        status: 405,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("API Error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Server error",
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
