// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { corsHeaders } from "../_shared/config.ts";
import { createClient } from "jsr:@supabase/supabase-js";
import Stripe from "https://esm.sh/stripe@12.0.0?target=deno";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// Initialize Stripe
const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
  apiVersion: "2023-10-16",
  httpClient: Stripe.createFetchHttpClient(),
});

const supabaseClient = createClient(Deno.env.get("DB_URL")!, Deno.env.get("SERVICE_ROLE_KEY")!);

serve(async (req: Request) => {
  const url = new URL(req.url);
  
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (req.method === "POST") {
      const {
        userId,
        email,
        items,
        subtotal,
        taxAmount,
        shippingCost,
        totalAmount,
        currency = "gbp",
        discountCode,
        isDiscounted,
        originalAmount,
        discountAmount
      } = await req.json();

      // Validate required fields
      if (!userId || !email || !items || !Array.isArray(items) || items.length === 0) {
        return new Response(JSON.stringify({ success: false, error: "Missing required fields" }), {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        });
      }

      // Validate amount
      const paymentAmount = Number(totalAmount);
      if (isNaN(paymentAmount) || paymentAmount <= 0) {
        return new Response(JSON.stringify({ success: false, error: "Invalid amount" }), {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        });
      }

      // Create or retrieve a customer
      const customers = await stripe.customers.list({ email });
      const customer = customers.data.length > 0 ? customers.data[0] : await stripe.customers.create({ email });

      // Create PaymentIntent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(paymentAmount * 100),
        currency: currency.toLowerCase(),
        customer: customer.id,
        metadata: {
          email,
          user_id: String(userId),
          order_type: "shop_order",
          items_count: String(items.length),
          subtotal: String(subtotal || 0),
          tax_amount: String(taxAmount || 0),
          shipping_cost: String(shippingCost || 0),
          discount_code: discountCode || "",
          is_discounted: String(isDiscounted || false),
          discount_amount: String(discountAmount || 0),
          original_amount: String(originalAmount || totalAmount),
        },
        receipt_email: email,
      });

      return new Response(
        JSON.stringify({
          success: true,
          customerId: customer.id,
          metadata: paymentIntent.metadata,
          clientSecret: paymentIntent.client_secret,
        }),
        { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    if (req.method === "GET") {
      const paymentIntentId = url.searchParams.get("payment_intent");

      if (!paymentIntentId) {
        return new Response(JSON.stringify({ error: "Payment intent ID is required" }), { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      const charge = paymentIntent.latest_charge as string;

      if (charge) {
        const chargeDetails = await stripe.charges.retrieve(charge);
        return new Response(JSON.stringify({ receiptUrl: chargeDetails.receipt_url }), { status: 200, headers: { ...corsHeaders, "Content-Type": "application/json" } });
      }

      return new Response(JSON.stringify({ error: "No receipt available" }), { status: 404, headers: { ...corsHeaders, "Content-Type": "application/json" } });
    }

    return new Response(JSON.stringify({ error: `Method ${req.method} not allowed on this path` }), { status: 405, headers: { ...corsHeaders, "Content-Type": "application/json" } });
  } catch (error) {
    console.error("API Error:", error);
    return new Response(JSON.stringify({ success: false, error: error instanceof Error ? error.message : "Server error" }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
